# Video Extraction Setup Guide

## Current Status

The video extraction functionality has been updated with the following improvements:

### ✅ Working Platforms
- **YouTube**: Fully functional using `ytdl-core`

### ⚠️ Platforms Requiring Additional Setup
- **Instagram**: Requires authentication or public post access
- **Facebook**: Requires Graph API setup or public video access
- **X/Twitter**: Requires API authentication or public tweet access

## What Was Fixed

1. **Removed deprecated dependencies**: Eliminated Instagram Private API and Twitter API v2 direct usage
2. **Added play-dl integration**: Primary method for social media video extraction
3. **Improved error handling**: Better error messages and fallback mechanisms
4. **Added comprehensive logging**: Better debugging information

## Setup Instructions

### For Instagram Videos

#### Option 1: Public Posts Only (Recommended)
- The current implementation attempts to extract public Instagram videos
- Ensure the Instagram post is publicly accessible
- Some posts may still fail due to Instagram's anti-scraping measures

#### Option 2: Instagram Basic Display API (Advanced)
```bash
# Install additional dependencies
npm install instagram-basic-display-api

# Set up environment variables
INSTAGRAM_APP_ID=your_app_id
INSTAGRAM_APP_SECRET=your_app_secret
INSTAGRAM_ACCESS_TOKEN=your_access_token
```

### For Facebook Videos

#### Option 1: Public Videos Only (Recommended)
- Ensure the Facebook video is publicly accessible
- The current implementation attempts to extract public videos

#### Option 2: Facebook Graph API (Advanced)
```bash
# Set up environment variables
FACEBOOK_ACCESS_TOKEN=your_access_token
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
```

### For X/Twitter Videos

#### Option 1: Public Tweets Only (Recommended)
- Ensure the tweet is publicly accessible
- The current implementation attempts to extract public videos

#### Option 2: Twitter API v2 (Advanced)
```bash
# Set up environment variables
TWITTER_BEARER_TOKEN=your_bearer_token
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
```

## Alternative Solutions

### Using yt-dlp (Recommended for Production)

For the most reliable video extraction across all platforms, consider using `yt-dlp`:

```bash
# Install yt-dlp
pip install yt-dlp

# Use via Node.js child_process
const { exec } = require('child_process');

function extractWithYtDlp(url) {
  return new Promise((resolve, reject) => {
    exec(`yt-dlp -f best --get-url "${url}"`, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve(stdout.trim());
      }
    });
  });
}
```

## Testing

Run the test file to verify functionality:

```bash
node test-video-extraction.js
```

## Troubleshooting

### Common Issues

1. **"play-dl failed" errors**: This is expected for some platforms and the code will attempt fallback methods
2. **Authentication errors**: Set up proper API credentials for the respective platforms
3. **Rate limiting**: Social media platforms may rate limit requests

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=video-extraction node your-app.js
```

## Production Recommendations

1. **Use yt-dlp**: Most reliable for all platforms
2. **Implement caching**: Cache video URLs to reduce API calls
3. **Add retry logic**: Implement exponential backoff for failed requests
4. **Monitor rate limits**: Track API usage for each platform
5. **Use webhooks**: For real-time video processing, consider platform webhooks

## Security Notes

- Never commit API keys to version control
- Use environment variables for sensitive data
- Implement proper error handling to avoid exposing internal details
- Consider using a proxy service for video extraction in production
