{"name": "video_play_management", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@distube/ytdl-core": "^4.16.12", "axios": "^1.10.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "fluent-ffmpeg": "^2.1.3", "instagram-private-api": "^1.46.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nanospinner": "^1.2.2", "nodemon": "^3.1.10", "pg": "^8.16.0", "play-dl": "^1.9.7", "stream": "^0.0.3", "twitter-api-v2": "^1.23.2", "util": "^0.12.5", "uuid": "^11.1.0", "ytdl-core": "npm:@distube/ytdl-core@^4.16.12"}}