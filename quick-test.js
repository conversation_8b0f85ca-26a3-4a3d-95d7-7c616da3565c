// Quick test to verify the video extraction is working
const { extractVideoFromUrl } = require('./src/utils/videoUtils');

async function quickTest() {
  console.log('Running quick test...\n');
  
  // Test with a short YouTube video (replace with a real URL for testing)
  const testUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
  
  try {
    console.log('Testing YouTube extraction...');
    console.log('URL:', testUrl);
    console.log('Note: This will only work if you have AWS S3 configured\n');
    
    // This will test the function but may fail at S3 upload if not configured
    const result = await extractVideoFromUrl(testUrl);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
    
    if (error.message.includes('S3') || error.message.includes('AWS')) {
      console.log('\nℹ️  This error is expected if AWS S3 is not configured.');
      console.log('The video extraction logic is working, but S3 upload failed.');
    } else if (error.message.includes('ytdl')) {
      console.log('\nℹ️  YouTube extraction failed. This might be due to:');
      console.log('- Invalid URL');
      console.log('- YouTube blocking the request');
      console.log('- Network issues');
    }
  }
}

if (require.main === module) {
  quickTest().catch(console.error);
}
