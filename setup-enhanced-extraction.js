const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function setupEnhancedExtraction() {
  console.log('🚀 Setting up Enhanced Video Extraction\n');
  
  // Check current setup
  console.log('1. Checking current setup...');
  
  // Check if yt-dlp is installed
  let ytDlpInstalled = false;
  try {
    const { stdout } = await execAsync('yt-dlp --version');
    console.log('   ✅ yt-dlp is installed:', stdout.trim());
    ytDlpInstalled = true;
  } catch (error) {
    console.log('   ❌ yt-dlp is not installed');
  }
  
  // Check if Python is available
  let pythonAvailable = false;
  try {
    const { stdout } = await execAsync('python --version');
    console.log('   ✅ Python is available:', stdout.trim());
    pythonAvailable = true;
  } catch (error) {
    try {
      const { stdout } = await execAsync('python3 --version');
      console.log('   ✅ Python3 is available:', stdout.trim());
      pythonAvailable = true;
    } catch (error2) {
      console.log('   ❌ Python is not available');
    }
  }
  
  // Check if pip is available
  let pipAvailable = false;
  try {
    await execAsync('pip --version');
    console.log('   ✅ pip is available');
    pipAvailable = true;
  } catch (error) {
    try {
      await execAsync('pip3 --version');
      console.log('   ✅ pip3 is available');
      pipAvailable = true;
    } catch (error2) {
      console.log('   ❌ pip is not available');
    }
  }
  
  console.log('\n2. Setup recommendations:\n');
  
  if (!pythonAvailable) {
    console.log('❌ CRITICAL: Python is required for yt-dlp');
    console.log('   Please install Python from: https://www.python.org/downloads/');
    console.log('   Or use your system package manager (brew, apt, etc.)\n');
  }
  
  if (!pipAvailable && pythonAvailable) {
    console.log('❌ CRITICAL: pip is required to install yt-dlp');
    console.log('   pip should come with Python. Try reinstalling Python.\n');
  }
  
  if (!ytDlpInstalled && pythonAvailable && pipAvailable) {
    console.log('⚠️  RECOMMENDED: Install yt-dlp for best results');
    console.log('   Run: pip install yt-dlp');
    console.log('   Or: pip3 install yt-dlp\n');
    
    // Offer to install yt-dlp
    console.log('Would you like to install yt-dlp now? (This will run: pip install yt-dlp)');
    console.log('Press Ctrl+C to cancel, or wait 10 seconds to auto-install...');
    
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    try {
      console.log('Installing yt-dlp...');
      const { stdout, stderr } = await execAsync('pip install yt-dlp');
      console.log('✅ yt-dlp installed successfully!');
      if (stdout) console.log(stdout);
    } catch (error) {
      console.log('❌ Failed to install yt-dlp automatically');
      console.log('Please run manually: pip install yt-dlp');
      if (error.stderr) console.log('Error:', error.stderr);
    }
  }
  
  // Check environment variables
  console.log('\n3. Environment variables (optional):');
  
  const fbToken = process.env.FACEBOOK_ACCESS_TOKEN;
  if (fbToken) {
    console.log('   ✅ FACEBOOK_ACCESS_TOKEN is set');
  } else {
    console.log('   ⚠️  FACEBOOK_ACCESS_TOKEN not set (optional)');
    console.log('      Set this for Facebook Graph API access');
  }
  
  // Create .env template if it doesn't exist
  if (!fs.existsSync('.env')) {
    console.log('\n4. Creating .env template...');
    const envTemplate = `# Facebook Graph API (optional)
# Get token from: https://developers.facebook.com/tools/explorer/
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token_here

# AWS S3 Configuration (required for video upload)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
S3_BUCKET_NAME=your_s3_bucket_name
`;
    
    fs.writeFileSync('.env', envTemplate);
    console.log('   ✅ Created .env template file');
    console.log('   Please edit .env with your actual credentials');
  } else {
    console.log('   ✅ .env file already exists');
  }
  
  console.log('\n5. Usage:');
  console.log('   Test enhanced extraction: node test-enhanced-facebook.js');
  console.log('   Use in your code:');
  console.log('   const { extractVideoFromUrl } = require("./src/utils/videoUtilsEnhanced");');
  
  console.log('\n🎉 Setup complete!');
  
  if (ytDlpInstalled || pythonAvailable) {
    console.log('✅ Your system is ready for enhanced video extraction');
  } else {
    console.log('⚠️  Install Python and yt-dlp for best results');
  }
}

if (require.main === module) {
  setupEnhancedExtraction().catch(console.error);
}

module.exports = { setupEnhancedExtraction };
