process.env.YTDL_NO_UPDATE = "true";

const ytdl = require("ytdl-core");
const fs = require("fs");
const axios = require("axios");
const { v4 } = require("uuid");
const { uploadToS3 } = require("./uploadToS3");

exports.extractVideoFromUrl = async (url) => {
  if (url.includes("youtube.com") || url.includes("youtu.be")) {
    return await extractVideoFromYouTube(url);
  } else if (url.includes("instagram.com")) {
    return await extractVideoFromInstagram(url);
  } else if (url.includes("facebook.com")) {
    return await extractVideoFromFacebook(url);
  } else if (url.includes("twitter.com") || url.includes("x.com")) {
    return await extractVideoFromX(url);
  } else {
    throw new Error("Unsupported video platform");
  }
};

async function extractVideoFromYouTube(url) {
  try {
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;

    await new Promise((resolve, reject) => {
      ytdl(url, { quality: 'highest' })
        .pipe(fs.createWriteStream(localFilePath))
        .on('finish', resolve)
        .on('error', reject);
    });

    const videoBuffer = fs.createReadStream(localFilePath);

    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });

    return videoData;
  } catch (error) {
    console.error("Error extracting YouTube video:", error.message);
    if (error.message.includes("Could not extract functions")) {
      throw new Error("Failed to extract video due to signature extraction issues from YouTube. Please try again later.");
    }
    throw new Error("Error extracting YouTube video");
  }
}

async function extractVideoFromInstagram(url) {
  try {
    console.log("Attempting Instagram video extraction for:", url);

    // Try alternative method for Instagram videos
    const videoData = await extractInstagramVideoAlternative(url);
    return videoData;

  } catch (error) {
    console.error("Error extracting Instagram video:", error);
    throw new Error(`Error extracting Instagram video: ${error.message}`);
  }
}

async function extractInstagramVideoAlternative(url) {
  try {
    // Add ?__a=1 to get JSON response from Instagram
    const jsonUrl = url.includes('?') ? `${url}&__a=1` : `${url}?__a=1`;

    const response = await axios.get(jsonUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json'
      }
    });

    let videoUrl = null;

    // Try to extract video URL from JSON response
    if (response.data && response.data.graphql) {
      const media = response.data.graphql.shortcode_media;
      if (media && media.is_video && media.video_url) {
        videoUrl = media.video_url;
      }
    }

    if (!videoUrl) {
      // Fallback: scrape the HTML page
      const htmlResponse = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const html = htmlResponse.data;

      // Look for video URLs in the HTML
      const videoUrlPatterns = [
        /"video_url":"([^"]+)"/,
        /"playback_url":"([^"]+)"/,
        /property="og:video" content="([^"]+)"/,
        /property="og:video:secure_url" content="([^"]+)"/
      ];

      for (const pattern of videoUrlPatterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
          videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
          break;
        }
      }
    }

    if (!videoUrl) {
      throw new Error("Could not extract video URL from Instagram post");
    }

    console.log("Found Instagram video URL:", videoUrl.substring(0, 100) + "...");

    const fileName = `${v4()}.mp4`;
    return await downloadAndUploadVideo(videoUrl, fileName);

  } catch (error) {
    console.error("Instagram alternative extraction failed:", error.message);
    throw new Error(`Instagram video extraction failed: ${error.message}. This may be due to privacy settings, login requirements, or Instagram's anti-scraping measures.`);
  }
}

async function extractVideoFromFacebook(url) {
  try {
    console.log("Attempting Facebook video extraction for:", url);

    // Try alternative method for Facebook videos
    const videoData = await extractFacebookVideoAlternative(url);
    return videoData;

  } catch (error) {
    console.error("Error extracting Facebook video:", error);
    throw new Error(`Error extracting Facebook video: ${error.message}`);
  }
}

async function extractFacebookVideoAlternative(url) {
  try {
    // Method 1: Try to extract video ID and use a different approach
    let videoId = null;

    // Extract video ID from different Facebook URL formats
    const patterns = [
      /facebook\.com\/watch\/?\?v=(\d+)/,
      /facebook\.com\/.*\/videos\/(\d+)/,
      /facebook\.com\/video\.php\?v=(\d+)/,
      /facebook\.com\/.*\/posts\/(\d+)/,
      /facebook\.com\/watch\/?\?.*v=(\d+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        videoId = match[1];
        break;
      }
    }

    if (!videoId) {
      throw new Error("Could not extract video ID from Facebook URL");
    }

    console.log("Extracted Facebook video ID:", videoId);

    // Method 2: Try to get video info using a web scraping approach
    const videoInfo = await scrapeFacebookVideo(url);

    if (videoInfo && videoInfo.videoUrl) {
      const fileName = `${v4()}.mp4`;
      return await downloadAndUploadVideo(videoInfo.videoUrl, fileName);
    }

    throw new Error("Could not extract video URL from Facebook page");

  } catch (error) {
    console.error("Facebook alternative extraction failed:", error.message);
    throw new Error(`Facebook video extraction failed: ${error.message}. This may be due to privacy settings, region restrictions, or Facebook's anti-scraping measures.`);
  }
}

async function scrapeFacebookVideo(url) {
  try {
    // This is a simplified approach - in production, you might want to use Puppeteer
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const html = response.data;

    // Look for video URLs in the HTML
    const videoUrlPatterns = [
      /"playable_url":"([^"]+)"/,
      /"browser_native_hd_url":"([^"]+)"/,
      /"browser_native_sd_url":"([^"]+)"/,
      /hd_src:"([^"]+)"/,
      /sd_src:"([^"]+)"/
    ];

    for (const pattern of videoUrlPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        // Decode the URL
        let videoUrl = match[1].replace(/\\u0025/g, '%').replace(/\\/g, '');
        videoUrl = decodeURIComponent(videoUrl);

        console.log("Found potential video URL:", videoUrl.substring(0, 100) + "...");
        return { videoUrl };
      }
    }

    return null;
  } catch (error) {
    console.error("Error scraping Facebook video:", error.message);
    return null;
  }
}

async function downloadAndUploadVideo(videoUrl, fileName) {
  try {
    const localFilePath = `./${fileName}`;

    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const writer = fs.createWriteStream(localFilePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', async () => {
        try {
          const videoBuffer = fs.createReadStream(localFilePath);
          const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

          // Clean up local file
          fs.unlink(localFilePath, (err) => {
            if (err) {
              console.error("Error deleting the local file:", err);
            } else {
              console.log("Local file deleted successfully.");
            }
          });

          resolve(videoData);
        } catch (uploadError) {
          reject(uploadError);
        }
      });
      writer.on('error', reject);
    });
  } catch (error) {
    throw new Error(`Error downloading video: ${error.message}`);
  }
}

async function extractVideoFromX(url) {
  try {
    console.log("Attempting X/Twitter video extraction for:", url);

    // Try alternative method for X/Twitter videos
    const videoData = await extractTwitterVideoAlternative(url);
    return videoData;

  } catch (error) {
    console.error("Error extracting X (Twitter) video:", error);
    throw new Error(`Error extracting X (Twitter) video: ${error.message}`);
  }
}

async function extractTwitterVideoAlternative(url) {
  try {
    // Convert x.com to twitter.com for consistency
    const twitterUrl = url.replace('x.com', 'twitter.com');

    const response = await axios.get(twitterUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const html = response.data;
    let videoUrl = null;

    // Look for video URLs in the HTML
    const videoUrlPatterns = [
      /"video_url":"([^"]+)"/,
      /"playback_url":"([^"]+)"/,
      /property="og:video" content="([^"]+)"/,
      /property="og:video:secure_url" content="([^"]+)"/,
      /"contentUrl":"([^"]+\.mp4[^"]*)"/,
      /data-expanded-url="([^"]*\.mp4[^"]*)"/
    ];

    for (const pattern of videoUrlPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        videoUrl = match[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
        // Ensure it's a video URL
        if (videoUrl.includes('.mp4') || videoUrl.includes('video')) {
          break;
        }
        videoUrl = null;
      }
    }

    if (!videoUrl) {
      // Try to find tweet data in script tags
      const scriptMatches = html.match(/<script[^>]*>window\.__INITIAL_STATE__=({.*?})<\/script>/);
      if (scriptMatches && scriptMatches[1]) {
        try {
          const data = JSON.parse(scriptMatches[1]);
          // Navigate through the Twitter data structure to find video URLs
          // This is a simplified approach - Twitter's structure can be complex
          const findVideoInData = (obj) => {
            if (typeof obj !== 'object' || obj === null) return null;

            if (obj.video_info && obj.video_info.variants) {
              const mp4Variants = obj.video_info.variants.filter(v => v.content_type === 'video/mp4');
              if (mp4Variants.length > 0) {
                // Get the highest bitrate version
                mp4Variants.sort((a, b) => (b.bitrate || 0) - (a.bitrate || 0));
                return mp4Variants[0].url;
              }
            }

            for (const key in obj) {
              const result = findVideoInData(obj[key]);
              if (result) return result;
            }

            return null;
          };

          videoUrl = findVideoInData(data);
        } catch (parseError) {
          console.log("Could not parse Twitter data:", parseError.message);
        }
      }
    }

    if (!videoUrl) {
      throw new Error("Could not extract video URL from Twitter/X post");
    }

    console.log("Found Twitter video URL:", videoUrl.substring(0, 100) + "...");

    const fileName = `${v4()}.mp4`;
    return await downloadAndUploadVideo(videoUrl, fileName);

  } catch (error) {
    console.error("Twitter alternative extraction failed:", error.message);
    throw new Error(`Twitter/X video extraction failed: ${error.message}. This may be due to privacy settings, login requirements, or Twitter's anti-scraping measures.`);
  }
}




