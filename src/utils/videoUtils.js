process.env.YTDL_NO_UPDATE = "true";

const ytdl = require("ytdl-core");
const fs = require("fs");
const { v4 } = require("uuid");
const { uploadToS3 } = require("./uploadToS3");
const playdl = require('play-dl');

exports.extractVideoFromUrl = async (url) => {
  if (url.includes("youtube.com") || url.includes("youtu.be")) {
    return await extractVideoFromYouTube(url);
  } else if (url.includes("instagram.com")) {
    return await extractVideoFromInstagram(url);
  } else if (url.includes("facebook.com")) {
    return await extractVideoFromFacebook(url);
  } else if (url.includes("twitter.com") || url.includes("x.com")) {
    return await extractVideoFromX(url);
  } else {
    throw new Error("Unsupported video platform");
  }
};

async function extractVideoFromYouTube(url) {
  try {
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;

    await new Promise((resolve, reject) => {
      ytdl(url, { quality: 'highest' })
        .pipe(fs.createWriteStream(localFilePath))
        .on('finish', resolve)
        .on('error', reject);
    });

    const videoBuffer = fs.createReadStream(localFilePath);

    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });

    return videoData;
  } catch (error) {
    console.error("Error extracting YouTube video:", error.message);
    if (error.message.includes("Could not extract functions")) {
      throw new Error("Failed to extract video due to signature extraction issues from YouTube. Please try again later.");
    }
    throw new Error("Error extracting YouTube video");
  }
}

async function extractVideoFromInstagram(url) {
  try {
    // First try using play-dl library for Instagram video extraction
    try {
      const info = await playdl.video_info(url);

      if (info && info.video_details && info.video_details.url) {
        const fileName = `${v4()}.mp4`;
        const localFilePath = `./${fileName}`;

        // Download the video stream
        const stream = await playdl.stream(url, { quality: 'highest' });

        await new Promise((resolve, reject) => {
          const writeStream = fs.createWriteStream(localFilePath);
          stream.stream.pipe(writeStream)
            .on('finish', resolve)
            .on('error', reject);
        });

        const videoBuffer = fs.createReadStream(localFilePath);
        const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

        // Clean up local file
        fs.unlink(localFilePath, (err) => {
          if (err) {
            console.error("Error deleting the local file:", err);
          } else {
            console.log("Local file deleted successfully.");
          }
        });

        return videoData;
      }
    } catch (playdlError) {
      console.log("play-dl failed for Instagram, trying alternative method:", playdlError.message);
    }

    // Fallback: Try to extract video URL using a different approach
    // This is a simplified approach - in production, you might want to use a more robust solution
    throw new Error("Instagram video extraction currently requires additional setup. Please ensure the URL is accessible and try again.");

  } catch (error) {
    console.error("Error extracting Instagram video:", error);
    throw new Error(`Error extracting Instagram video: ${error.message}`);
  }
}

async function extractVideoFromFacebook(url) {
  try {
    // First try using play-dl library for Facebook video extraction
    try {
      const info = await playdl.video_info(url);

      if (info && info.video_details && info.video_details.url) {
        const fileName = `${v4()}.mp4`;
        const localFilePath = `./${fileName}`;

        // Download the video stream
        const stream = await playdl.stream(url, { quality: 'highest' });

        await new Promise((resolve, reject) => {
          const writeStream = fs.createWriteStream(localFilePath);
          stream.stream.pipe(writeStream)
            .on('finish', resolve)
            .on('error', reject);
        });

        const videoBuffer = fs.createReadStream(localFilePath);
        const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

        // Clean up local file
        fs.unlink(localFilePath, (err) => {
          if (err) {
            console.error("Error deleting the local file:", err);
          } else {
            console.log("Local file deleted successfully.");
          }
        });

        return videoData;
      }
    } catch (playdlError) {
      console.log("play-dl failed for Facebook, trying alternative method:", playdlError.message);
    }

    // Fallback: Facebook videos often require authentication or special handling
    throw new Error("Facebook video extraction currently requires additional setup. Please ensure the video is publicly accessible and try again.");

  } catch (error) {
    console.error("Error extracting Facebook video:", error);
    throw new Error(`Error extracting Facebook video: ${error.message}`);
  }
}

async function extractVideoFromX(url) {
  try {
    // First try using play-dl library for Twitter/X video extraction
    try {
      const info = await playdl.video_info(url);

      if (info && info.video_details && info.video_details.url) {
        const fileName = `${v4()}.mp4`;
        const localFilePath = `./${fileName}`;

        // Download the video stream
        const stream = await playdl.stream(url, { quality: 'highest' });

        await new Promise((resolve, reject) => {
          const writeStream = fs.createWriteStream(localFilePath);
          stream.stream.pipe(writeStream)
            .on('finish', resolve)
            .on('error', reject);
        });

        const videoBuffer = fs.createReadStream(localFilePath);
        const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

        // Clean up local file
        fs.unlink(localFilePath, (err) => {
          if (err) {
            console.error("Error deleting the local file:", err);
          } else {
            console.log("Local file deleted successfully.");
          }
        });

        return videoData;
      }
    } catch (playdlError) {
      console.log("play-dl failed for X/Twitter, trying alternative method:", playdlError.message);
    }

    // Fallback: X/Twitter videos often require authentication
    throw new Error("X/Twitter video extraction currently requires additional setup. Please ensure the tweet is publicly accessible and try again.");

  } catch (error) {
    console.error("Error extracting X (Twitter) video:", error);
    throw new Error(`Error extracting X (Twitter) video: ${error.message}`);
  }
}




