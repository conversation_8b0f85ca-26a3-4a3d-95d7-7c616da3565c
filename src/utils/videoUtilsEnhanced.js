process.env.YTDL_NO_UPDATE = "true";

const ytdl = require("ytdl-core");
const fs = require("fs");
const axios = require("axios");
const { v4 } = require("uuid");
const { uploadToS3 } = require("./uploadToS3");
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

exports.extractVideoFromUrl = async (url) => {
  if (url.includes("youtube.com") || url.includes("youtu.be")) {
    return await extractVideoFromYouTube(url);
  } else if (url.includes("instagram.com")) {
    return await extractVideoFromInstagram(url);
  } else if (url.includes("facebook.com")) {
    return await extractVideoFromFacebook(url);
  } else if (url.includes("twitter.com") || url.includes("x.com")) {
    return await extractVideoFromX(url);
  } else {
    throw new Error("Unsupported video platform");
  }
};

// YouTube extraction (unchanged)
async function extractVideoFromYouTube(url) {
  try {
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;

    await new Promise((resolve, reject) => {
      ytdl(url, { quality: 'highest' })
        .pipe(fs.createWriteStream(localFilePath))
        .on('finish', resolve)
        .on('error', reject);
    });

    const videoBuffer = fs.createReadStream(localFilePath);
    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });

    return videoData;
  } catch (error) {
    console.error("Error extracting YouTube video:", error.message);
    if (error.message.includes("Could not extract functions")) {
      throw new Error("Failed to extract video due to signature extraction issues from YouTube. Please try again later.");
    }
    throw new Error("Error extracting YouTube video");
  }
}

// Enhanced extraction functions with yt-dlp fallback
async function extractVideoFromInstagram(url) {
  try {
    console.log("Attempting Instagram video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractInstagramVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Instagram');
    }
  } catch (error) {
    console.error("Error extracting Instagram video:", error);
    throw new Error(`Error extracting Instagram video: ${error.message}`);
  }
}

async function extractVideoFromFacebook(url) {
  try {
    console.log("Attempting Facebook video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractFacebookVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Facebook');
    }
  } catch (error) {
    console.error("Error extracting Facebook video:", error);
    throw new Error(`Error extracting Facebook video: ${error.message}`);
  }
}

async function extractVideoFromX(url) {
  try {
    console.log("Attempting X/Twitter video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractTwitterVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Twitter');
    }
  } catch (error) {
    console.error("Error extracting X (Twitter) video:", error);
    throw new Error(`Error extracting X (Twitter) video: ${error.message}`);
  }
}

// yt-dlp fallback method
async function extractWithYtDlp(url, platform) {
  try {
    console.log(`Trying yt-dlp for ${platform} video...`);
    
    // Check if yt-dlp is installed
    let ytDlpPath = 'yt-dlp';
    try {
      await execAsync('yt-dlp --version');
    } catch (error) {
      // Try common user installation paths
      const commonPaths = [
        '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',
        '/usr/local/bin/yt-dlp',
        '~/.local/bin/yt-dlp'
      ];

      let found = false;
      for (const path of commonPaths) {
        try {
          await execAsync(`${path} --version`);
          ytDlpPath = path;
          found = true;
          break;
        } catch (pathError) {
          continue;
        }
      }

      if (!found) {
        throw new Error(`yt-dlp is not installed or not in PATH. Please install it with: pip install yt-dlp`);
      }
    }
    
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;

    // Use yt-dlp to download the video
    const command = `${ytDlpPath} -f "best[ext=mp4]" -o "${localFilePath}" "${url}"`;

    console.log('Executing yt-dlp command...');
    const { stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      console.error('yt-dlp stderr:', stderr);
    }
    
    // Check if file was created
    if (!fs.existsSync(localFilePath)) {
      throw new Error('yt-dlp failed to download the video');
    }
    
    const videoBuffer = fs.createReadStream(localFilePath);
    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);
    
    // Clean up local file
    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });
    
    return videoData;
    
  } catch (error) {
    throw new Error(`yt-dlp extraction failed: ${error.message}`);
  }
}

// Enhanced Facebook extraction with multiple fallback methods
async function extractFacebookVideoAlternative(url) {
  try {
    console.log("Starting enhanced Facebook extraction for:", url);

    // Method 1: Try direct video URL extraction
    try {
      const directResult = await extractFacebookDirect(url);
      if (directResult) return directResult;
    } catch (error) {
      console.log("Direct extraction failed:", error.message);
    }

    // Method 2: Try mobile Facebook URL
    try {
      const mobileResult = await extractFacebookMobile(url);
      if (mobileResult) return mobileResult;
    } catch (error) {
      console.log("Mobile extraction failed:", error.message);
    }

    // Method 3: Try embedded video extraction
    try {
      const embedResult = await extractFacebookEmbed(url);
      if (embedResult) return embedResult;
    } catch (error) {
      console.log("Embed extraction failed:", error.message);
    }

    // Method 4: Try Graph API approach (if configured)
    try {
      const graphResult = await extractFacebookGraph(url);
      if (graphResult) return graphResult;
    } catch (error) {
      console.log("Graph API extraction failed:", error.message);
    }

    throw new Error("All Facebook extraction methods failed");

  } catch (error) {
    throw new Error(`Facebook video extraction failed: ${error.message}. Consider using yt-dlp for better reliability.`);
  }
}

// Method 1: Direct extraction from main Facebook page
async function extractFacebookDirect(url) {
  const response = await axios.get(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    }
  });

  const html = response.data;

  // Enhanced patterns for video URL extraction
  const patterns = [
    /"playable_url":"([^"]+)"/g,
    /"browser_native_hd_url":"([^"]+)"/g,
    /"browser_native_sd_url":"([^"]+)"/g,
    /hd_src:"([^"]+)"/g,
    /sd_src:"([^"]+)"/g,
    /"video_url":"([^"]+)"/g,
    /videoUrl":"([^"]+)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /src:"([^"]*\.mp4[^"]*)"/g
  ];

  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let videoUrl = match[1];
      if (videoUrl && videoUrl.includes('http')) {
        // Clean up the URL
        videoUrl = videoUrl.replace(/\\u0025/g, '%')
                          .replace(/\\u0026/g, '&')
                          .replace(/\\\//g, '/')
                          .replace(/\\/g, '');

        try {
          videoUrl = decodeURIComponent(videoUrl);
          console.log("Found potential video URL:", videoUrl.substring(0, 100) + "...");

          // Validate the URL
          if (await validateVideoUrl(videoUrl)) {
            const fileName = `${v4()}.mp4`;
            return await downloadAndUploadVideo(videoUrl, fileName);
          }
        } catch (decodeError) {
          console.log("URL decode failed:", decodeError.message);
        }
      }
    }
  }

  return null;
}

// Method 2: Try mobile Facebook version
async function extractFacebookMobile(url) {
  const mobileUrl = url.replace('www.facebook.com', 'm.facebook.com');

  const response = await axios.get(mobileUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    }
  });

  const html = response.data;

  // Mobile-specific patterns
  const mobilePatterns = [
    /src="([^"]*\.mp4[^"]*)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /data-sigil="inlineVideo"[^>]*src="([^"]+)"/g
  ];

  for (const pattern of mobilePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const videoUrl = match[1];
      if (videoUrl && await validateVideoUrl(videoUrl)) {
        console.log("Found mobile video URL:", videoUrl.substring(0, 100) + "...");
        const fileName = `${v4()}.mp4`;
        return await downloadAndUploadVideo(videoUrl, fileName);
      }
    }
  }

  return null;
}

// Method 3: Try Facebook embed approach
async function extractFacebookEmbed(url) {
  // Extract video ID
  const videoIdMatch = url.match(/(?:videos\/|watch\/?\?v=)(\d+)/);
  if (!videoIdMatch) return null;

  const videoId = videoIdMatch[1];
  const embedUrl = `https://www.facebook.com/plugins/video.php?href=${encodeURIComponent(url)}`;

  const response = await axios.get(embedUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  });

  const html = response.data;

  // Look for video sources in embed
  const embedPatterns = [
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /src="([^"]*\.mp4[^"]*)"/g
  ];

  for (const pattern of embedPatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const videoUrl = match[1].replace(/\\/g, '');
      if (videoUrl && await validateVideoUrl(videoUrl)) {
        console.log("Found embed video URL:", videoUrl.substring(0, 100) + "...");
        const fileName = `${v4()}.mp4`;
        return await downloadAndUploadVideo(videoUrl, fileName);
      }
    }
  }

  return null;
}

// Method 4: Facebook Graph API (requires access token)
async function extractFacebookGraph(url) {
  const accessToken = process.env.FACEBOOK_ACCESS_TOKEN;
  if (!accessToken) {
    throw new Error("Facebook Graph API access token not configured");
  }

  const videoIdMatch = url.match(/(?:videos\/|watch\/?\?v=)(\d+)/);
  if (!videoIdMatch) {
    throw new Error("Could not extract video ID for Graph API");
  }

  const videoId = videoIdMatch[1];
  const graphUrl = `https://graph.facebook.com/v18.0/${videoId}?fields=source&access_token=${accessToken}`;

  const response = await axios.get(graphUrl);

  if (response.data && response.data.source) {
    console.log("Found Graph API video URL");
    const fileName = `${v4()}.mp4`;
    return await downloadAndUploadVideo(response.data.source, fileName);
  }

  return null;
}

// Utility function to validate video URLs
async function validateVideoUrl(url) {
  try {
    const response = await axios.head(url, { timeout: 5000 });
    const contentType = response.headers['content-type'];
    return contentType && contentType.includes('video');
  } catch (error) {
    return false;
  }
}

// Enhanced download function with better error handling
async function downloadAndUploadVideo(videoUrl, fileName) {
  try {
    const localFilePath = `./${fileName}`;

    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.facebook.com/'
      },
      timeout: 30000
    });

    const writer = fs.createWriteStream(localFilePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', async () => {
        try {
          // Check if file was actually written
          const stats = fs.statSync(localFilePath);
          if (stats.size === 0) {
            throw new Error("Downloaded file is empty");
          }

          const videoBuffer = fs.createReadStream(localFilePath);
          const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

          // Clean up local file
          fs.unlink(localFilePath, (err) => {
            if (err) {
              console.error("Error deleting the local file:", err);
            } else {
              console.log("Local file deleted successfully.");
            }
          });

          resolve(videoData);
        } catch (uploadError) {
          reject(uploadError);
        }
      });
      writer.on('error', reject);

      // Add timeout for download
      setTimeout(() => {
        writer.destroy();
        reject(new Error("Download timeout"));
      }, 60000);
    });
  } catch (error) {
    throw new Error(`Error downloading video: ${error.message}`);
  }
}

// Placeholder implementations for Instagram and Twitter
async function extractInstagramVideoAlternative() {
  throw new Error("Instagram extraction not implemented in enhanced version - use yt-dlp fallback");
}

async function extractTwitterVideoAlternative() {
  throw new Error("Twitter extraction not implemented in enhanced version - use yt-dlp fallback");
}
