process.env.YTDL_NO_UPDATE = "true";

const ytdl = require("ytdl-core");
const fs = require("fs");
const axios = require("axios");
const { v4 } = require("uuid");
const { uploadToS3 } = require("./uploadToS3");
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

exports.extractVideoFromUrl = async (url) => {
  if (url.includes("youtube.com") || url.includes("youtu.be")) {
    return await extractVideoFromYouTube(url);
  } else if (url.includes("instagram.com")) {
    return await extractVideoFromInstagram(url);
  } else if (url.includes("facebook.com")) {
    return await extractVideoFromFacebook(url);
  } else if (url.includes("twitter.com") || url.includes("x.com")) {
    return await extractVideoFromX(url);
  } else {
    throw new Error("Unsupported video platform");
  }
};

// YouTube extraction (unchanged)
async function extractVideoFromYouTube(url) {
  try {
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;

    await new Promise((resolve, reject) => {
      ytdl(url, { quality: 'highest' })
        .pipe(fs.createWriteStream(localFilePath))
        .on('finish', resolve)
        .on('error', reject);
    });

    const videoBuffer = fs.createReadStream(localFilePath);
    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);

    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });

    return videoData;
  } catch (error) {
    console.error("Error extracting YouTube video:", error.message);
    if (error.message.includes("Could not extract functions")) {
      throw new Error("Failed to extract video due to signature extraction issues from YouTube. Please try again later.");
    }
    throw new Error("Error extracting YouTube video");
  }
}

// Enhanced extraction functions with yt-dlp fallback
async function extractVideoFromInstagram(url) {
  try {
    console.log("Attempting Instagram video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractInstagramVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Instagram');
    }
  } catch (error) {
    console.error("Error extracting Instagram video:", error);
    throw new Error(`Error extracting Instagram video: ${error.message}`);
  }
}

async function extractVideoFromFacebook(url) {
  try {
    console.log("Attempting Facebook video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractFacebookVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Facebook');
    }
  } catch (error) {
    console.error("Error extracting Facebook video:", error);
    throw new Error(`Error extracting Facebook video: ${error.message}`);
  }
}

async function extractVideoFromX(url) {
  try {
    console.log("Attempting X/Twitter video extraction for:", url);
    
    // First try the custom method
    try {
      return await extractTwitterVideoAlternative(url);
    } catch (customError) {
      console.log("Custom method failed:", customError.message);
      
      // Fallback to yt-dlp if available
      return await extractWithYtDlp(url, 'Twitter');
    }
  } catch (error) {
    console.error("Error extracting X (Twitter) video:", error);
    throw new Error(`Error extracting X (Twitter) video: ${error.message}`);
  }
}

// yt-dlp fallback method
async function extractWithYtDlp(url, platform) {
  try {
    console.log(`Trying yt-dlp for ${platform} video...`);
    
    // Check if yt-dlp is installed
    try {
      await execAsync('yt-dlp --version');
    } catch (error) {
      throw new Error(`yt-dlp is not installed. Please install it with: pip install yt-dlp`);
    }
    
    const fileName = `${v4()}.mp4`;
    const localFilePath = `./${fileName}`;
    
    // Use yt-dlp to download the video
    const command = `yt-dlp -f "best[ext=mp4]" -o "${localFilePath}" "${url}"`;
    
    console.log('Executing yt-dlp command...');
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      console.error('yt-dlp stderr:', stderr);
    }
    
    // Check if file was created
    if (!fs.existsSync(localFilePath)) {
      throw new Error('yt-dlp failed to download the video');
    }
    
    const videoBuffer = fs.createReadStream(localFilePath);
    const videoData = await uploadToS3(videoBuffer, `test_videos/${fileName}`);
    
    // Clean up local file
    fs.unlink(localFilePath, (err) => {
      if (err) {
        console.error("Error deleting the local file:", err);
      } else {
        console.log("Local file deleted successfully.");
      }
    });
    
    return videoData;
    
  } catch (error) {
    throw new Error(`yt-dlp extraction failed: ${error.message}`);
  }
}

// Include all the alternative extraction methods from the original file
// (These would be copied from the main videoUtils.js file)
async function extractInstagramVideoAlternative(url) {
  // Implementation from the main file...
  throw new Error("Custom Instagram extraction not implemented in this enhanced version");
}

async function extractFacebookVideoAlternative(url) {
  // Implementation from the main file...
  throw new Error("Custom Facebook extraction not implemented in this enhanced version");
}

async function extractTwitterVideoAlternative(url) {
  // Implementation from the main file...
  throw new Error("Custom Twitter extraction not implemented in this enhanced version");
}
