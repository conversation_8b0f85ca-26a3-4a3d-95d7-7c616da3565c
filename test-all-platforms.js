const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test all social media platforms
async function testAllPlatforms() {
  console.log('🚀 Testing All Social Media Video Extraction Platforms');
  console.log('====================================================\n');
  
  // Check yt-dlp availability first
  console.log('🔧 Checking yt-dlp availability...');
  const ytDlpAvailable = await checkYtDlp();
  console.log('');
  
  // Test URLs for each platform
  const testData = {
    facebook: {
      name: '📘 Facebook',
      script: 'test-facebook-download-only.js',
      testUrls: [
        'https://www.facebook.com/watch?v=487026504219526' // Working test URL
      ]
    },
    instagram: {
      name: '📸 Instagram',
      script: 'test-instagram-download-only.js',
      testUrls: [
        'https://www.instagram.com/reel/DH5oNplz_cC/' // Working Instagram reel
      ]
    },
    twitter: {
      name: '🐦 X/Twitter',
      script: 'test-twitter-download-only.js',
      testUrls: [
        'https://x.com/i/status/1937465796814316002' // Working Twitter video
      ]
    }
  };
  
  // Test each platform
  for (const [platform, config] of Object.entries(testData)) {
    console.log(`\n${config.name} Testing`);
    console.log('='.repeat(30));
    
    // Check if we have real test URLs
    const hasRealUrls = config.testUrls.some(url => !url.includes('EXAMPLE'));
    
    if (!hasRealUrls && platform !== 'facebook') {
      console.log(`⚠️  No test URLs provided for ${config.name}`);
      console.log(`   Please edit test-all-platforms.js and add real ${platform} video URLs`);
      console.log(`   Example URLs needed:`);
      for (const url of config.testUrls) {
        console.log(`   - ${url}`);
      }
      continue;
    }
    
    try {
      console.log(`Running: node ${config.script}`);
      const { stdout, stderr } = await execAsync(`node ${config.script}`);
      
      if (stdout) {
        console.log(stdout);
      }
      
      if (stderr && !stderr.includes('WARNING')) {
        console.log('Errors:', stderr);
      }
      
    } catch (error) {
      console.log(`❌ Error testing ${config.name}:`, error.message);
    }
  }
  
  // Summary
  console.log('\n🎯 Testing Summary');
  console.log('==================');
  console.log('✅ Facebook: Ready to test (working test URL provided)');
  console.log('⚠️  Instagram: Needs real video URLs');
  console.log('⚠️  X/Twitter: Needs real video URLs');
  
  if (ytDlpAvailable) {
    console.log('✅ yt-dlp: Available as fallback method');
  } else {
    console.log('❌ yt-dlp: Not available (install with: pip install yt-dlp)');
  }
  
  console.log('\n📝 Next Steps:');
  console.log('1. Add real Instagram video URLs to test-instagram-download-only.js');
  console.log('2. Add real X/Twitter video URLs to test-twitter-download-only.js');
  console.log('3. Run individual platform tests: node test-[platform]-download-only.js');
  console.log('4. For production use, integrate with src/utils/videoUtilsEnhanced.js');
}

// Check yt-dlp availability
async function checkYtDlp() {
  try {
    await execAsync('yt-dlp --version');
    console.log('✅ yt-dlp is available in PATH');
    return true;
  } catch (error) {
    // Try common user installation paths
    const commonPaths = [
      '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',
      '/usr/local/bin/yt-dlp',
      '~/.local/bin/yt-dlp'
    ];
    
    for (const path of commonPaths) {
      try {
        await execAsync(`${path} --version`);
        console.log(`✅ yt-dlp is available at: ${path}`);
        return true;
      } catch (pathError) {
        continue;
      }
    }
    
    console.log('❌ yt-dlp is not installed');
    console.log('💡 Install with: pip install yt-dlp');
    return false;
  }
}

// Individual platform test functions
async function testFacebookOnly() {
  console.log('Testing Facebook only...\n');
  try {
    const { stdout } = await execAsync('node test-facebook-download-only.js');
    console.log(stdout);
  } catch (error) {
    console.log('Error:', error.message);
  }
}

async function testInstagramOnly() {
  console.log('Testing Instagram only...\n');
  try {
    const { stdout } = await execAsync('node test-instagram-download-only.js');
    console.log(stdout);
  } catch (error) {
    console.log('Error:', error.message);
  }
}

async function testTwitterOnly() {
  console.log('Testing X/Twitter only...\n');
  try {
    const { stdout } = await execAsync('node test-twitter-download-only.js');
    console.log(stdout);
  } catch (error) {
    console.log('Error:', error.message);
  }
}

// Command line interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    testAllPlatforms().catch(console.error);
  } else {
    const platform = args[0].toLowerCase();
    
    switch (platform) {
      case 'facebook':
      case 'fb':
        testFacebookOnly().catch(console.error);
        break;
      case 'instagram':
      case 'ig':
        testInstagramOnly().catch(console.error);
        break;
      case 'twitter':
      case 'x':
        testTwitterOnly().catch(console.error);
        break;
      default:
        console.log('Usage: node test-all-platforms.js [facebook|instagram|twitter]');
        console.log('');
        console.log('Examples:');
        console.log('  node test-all-platforms.js          # Test all platforms');
        console.log('  node test-all-platforms.js facebook # Test Facebook only');
        console.log('  node test-all-platforms.js instagram # Test Instagram only');
        console.log('  node test-all-platforms.js twitter  # Test X/Twitter only');
    }
  }
}
