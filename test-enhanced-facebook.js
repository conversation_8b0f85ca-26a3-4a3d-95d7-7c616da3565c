const { extractVideoFromUrl } = require('./src/utils/videoUtilsEnhanced');

async function testEnhancedFacebook() {
  console.log('Testing Enhanced Facebook Video Extraction...\n');
  
  // Test URLs - replace with actual Facebook video URLs
  const testUrls = [
    'https://www.facebook.com/watch?v=487026504219526',
    // Add more test URLs here
  ];
  
  for (const url of testUrls) {
    console.log(`\n--- Testing Facebook URL ---`);
    console.log(`URL: ${url}`);
    console.log('Starting enhanced extraction with multiple fallback methods...\n');
    
    try {
      const result = await extractVideoFromUrl(url);
      console.log('✅ SUCCESS! Video extracted:', result);
    } catch (error) {
      console.log('❌ All methods failed:', error.message);
      
      // Provide specific guidance
      if (error.message.includes('yt-dlp')) {
        console.log('\n💡 RECOMMENDATION: Install yt-dlp for better success rate:');
        console.log('   pip install yt-dlp');
        console.log('   The enhanced version will automatically use yt-dlp as fallback');
      } else if (error.message.includes('privacy settings')) {
        console.log('\n💡 TIP: Try with a public Facebook video that doesn\'t require login');
      } else if (error.message.includes('Graph API')) {
        console.log('\n💡 TIP: Set FACEBOOK_ACCESS_TOKEN environment variable for Graph API access');
      }
    }
  }
  
  console.log('\n--- Enhanced Facebook Extraction Summary ---');
  console.log('The enhanced version tries 4 different methods:');
  console.log('1. Direct page scraping with multiple patterns');
  console.log('2. Mobile Facebook version');
  console.log('3. Facebook embed extraction');
  console.log('4. Facebook Graph API (if token configured)');
  console.log('5. yt-dlp fallback (if installed)');
  console.log('\nFor best results, install yt-dlp: pip install yt-dlp');
}

// Test yt-dlp availability
async function checkYtDlp() {
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);
  
  try {
    await execAsync('yt-dlp --version');
    console.log('✅ yt-dlp is installed and available');
    return true;
  } catch (error) {
    console.log('❌ yt-dlp is not installed');
    console.log('💡 Install with: pip install yt-dlp');
    return false;
  }
}

async function main() {
  console.log('=== Enhanced Facebook Video Extraction Test ===\n');
  
  // Check yt-dlp availability first
  const ytDlpAvailable = await checkYtDlp();
  console.log('');
  
  if (!ytDlpAvailable) {
    console.log('⚠️  Without yt-dlp, success rate may be lower due to Facebook\'s anti-scraping measures');
    console.log('   The enhanced version will still try 4 different extraction methods\n');
  }
  
  await testEnhancedFacebook();
}

if (require.main === module) {
  main().catch(console.error);
}
