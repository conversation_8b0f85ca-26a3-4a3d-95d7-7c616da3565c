const axios = require('axios');
const fs = require('fs');
const { v4 } = require('uuid');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test Facebook video extraction without S3 upload
async function testFacebookDownloadOnly() {
  const url = 'https://www.facebook.com/watch?v=487026504219526';
  
  console.log('🎬 Testing Facebook Video Download (Local Only)');
  console.log('URL:', url);
  console.log('');
  
  try {
    // Method 1: Try direct extraction
    console.log('📥 Method 1: Direct Facebook extraction...');
    const directResult = await extractFacebookDirect(url);
    if (directResult) {
      console.log('✅ SUCCESS with direct method!');
      console.log('Downloaded file:', directResult);
      return;
    }
  } catch (error) {
    console.log('❌ Direct method failed:', error.message);
  }
  
  try {
    // Method 2: Try yt-dlp
    console.log('\n📥 Method 2: yt-dlp extraction...');
    const ytDlpResult = await extractWithYtDlpLocal(url);
    if (ytDlpResult) {
      console.log('✅ SUCCESS with yt-dlp!');
      console.log('Downloaded file:', ytDlpResult);
      return;
    }
  } catch (error) {
    console.log('❌ yt-dlp method failed:', error.message);
  }
  
  console.log('\n❌ All methods failed');
}

// Direct Facebook extraction (local download only)
async function extractFacebookDirect(url) {
  const response = await axios.get(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    }
  });
  
  const html = response.data;
  
  // Enhanced patterns for video URL extraction
  const patterns = [
    /"playable_url":"([^"]+)"/g,
    /"browser_native_hd_url":"([^"]+)"/g,
    /"browser_native_sd_url":"([^"]+)"/g,
    /hd_src:"([^"]+)"/g,
    /sd_src:"([^"]+)"/g,
    /"video_url":"([^"]+)"/g,
    /videoUrl":"([^"]+)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /src:"([^"]*\.mp4[^"]*)"/g
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let videoUrl = match[1];
      if (videoUrl && videoUrl.includes('http')) {
        // Clean up the URL
        videoUrl = videoUrl.replace(/\\u0025/g, '%')
                          .replace(/\\u0026/g, '&')
                          .replace(/\\\//g, '/')
                          .replace(/\\/g, '');
        
        try {
          videoUrl = decodeURIComponent(videoUrl);
          console.log("Found potential video URL:", videoUrl.substring(0, 100) + "...");
          
          // Validate the URL
          if (await validateVideoUrl(videoUrl)) {
            const fileName = `facebook_video_${Date.now()}.mp4`;
            const success = await downloadVideoLocal(videoUrl, fileName);
            if (success) {
              return fileName;
            }
          }
        } catch (decodeError) {
          console.log("URL decode failed:", decodeError.message);
        }
      }
    }
  }
  
  return null;
}

// yt-dlp extraction (local download only)
async function extractWithYtDlpLocal(url) {
  try {
    // Check if yt-dlp is available
    let ytDlpPath = 'yt-dlp';
    try {
      await execAsync('yt-dlp --version');
    } catch (error) {
      // Try common user installation paths
      const commonPaths = [
        '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',
        '/usr/local/bin/yt-dlp',
        '~/.local/bin/yt-dlp'
      ];
      
      let found = false;
      for (const path of commonPaths) {
        try {
          await execAsync(`${path} --version`);
          ytDlpPath = path;
          found = true;
          break;
        } catch (pathError) {
          continue;
        }
      }
      
      if (!found) {
        throw new Error('yt-dlp is not installed or not in PATH');
      }
    }
    
    const fileName = `facebook_video_ytdlp_${Date.now()}.mp4`;
    
    // Use yt-dlp to download the video
    const command = `${ytDlpPath} -f "best[ext=mp4]" -o "${fileName}" "${url}"`;
    
    console.log('Executing yt-dlp command...');
    const { stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      console.error('yt-dlp stderr:', stderr);
    }
    
    // Check if file was created
    if (fs.existsSync(fileName)) {
      const stats = fs.statSync(fileName);
      console.log(`File downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
      return fileName;
    } else {
      throw new Error('yt-dlp failed to download the video');
    }
    
  } catch (error) {
    throw new Error(`yt-dlp extraction failed: ${error.message}`);
  }
}

// Utility function to validate video URLs
async function validateVideoUrl(url) {
  try {
    const response = await axios.head(url, { timeout: 5000 });
    const contentType = response.headers['content-type'];
    return contentType && contentType.includes('video');
  } catch (error) {
    return false;
  }
}

// Download video locally
async function downloadVideoLocal(videoUrl, fileName) {
  try {
    console.log(`Downloading to: ${fileName}`);
    
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.facebook.com/'
      },
      timeout: 30000
    });
    
    const writer = fs.createWriteStream(fileName);
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // Check if file was actually written
        const stats = fs.statSync(fileName);
        if (stats.size === 0) {
          reject(new Error("Downloaded file is empty"));
        } else {
          console.log(`✅ Downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
          resolve(true);
        }
      });
      writer.on('error', reject);
      
      // Add timeout for download
      setTimeout(() => {
        writer.destroy();
        reject(new Error("Download timeout"));
      }, 60000);
    });
  } catch (error) {
    throw new Error(`Error downloading video: ${error.message}`);
  }
}

if (require.main === module) {
  testFacebookDownloadOnly().catch(console.error);
}
