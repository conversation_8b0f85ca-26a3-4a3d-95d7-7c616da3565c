const axios = require('axios');
const fs = require('fs');
const { v4 } = require('uuid');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test Instagram video extraction without S3 upload
async function testInstagramDownloadOnly() {
  // Test URLs - replace with actual Instagram video URLs
  const testUrls = [
    // 'https://www.instagram.com/p/EXAMPLE/', // Replace with actual Instagram post URL
    'https://www.instagram.com/reel/DH5oNplz_cC/', // Replace with actual Instagram reel URL https://www.instagram.com/reel/DH5oNplz_cC/?utm_source=ig_web_copy_link
  ];
  
  console.log('📸 Testing Instagram Video Download (Local Only)');
  console.log('');
  
  for (const url of testUrls) {
    if (url.includes('EXAMPLE')) {
      console.log('⚠️  Please replace EXAMPLE URLs with actual Instagram video URLs');
      continue;
    }
    
    console.log('URL:', url);
    console.log('');
    
    try {
      // Method 1: Try direct extraction
      console.log('📥 Method 1: Direct Instagram extraction...');
      const directResult = await extractInstagramDirect(url);
      if (directResult) {
        console.log('✅ SUCCESS with direct method!');
        console.log('Downloaded file:', directResult);
        continue;
      }
    } catch (error) {
      console.log('❌ Direct method failed:', error.message);
    }
    
    try {
      // Method 2: Try mobile Instagram
      console.log('\n📥 Method 2: Mobile Instagram extraction...');
      const mobileResult = await extractInstagramMobile(url);
      if (mobileResult) {
        console.log('✅ SUCCESS with mobile method!');
        console.log('Downloaded file:', mobileResult);
        continue;
      }
    } catch (error) {
      console.log('❌ Mobile method failed:', error.message);
    }
    
    try {
      // Method 3: Try yt-dlp
      console.log('\n📥 Method 3: yt-dlp extraction...');
      const ytDlpResult = await extractWithYtDlpLocal(url, 'Instagram');
      if (ytDlpResult) {
        console.log('✅ SUCCESS with yt-dlp!');
        console.log('Downloaded file:', ytDlpResult);
        continue;
      }
    } catch (error) {
      console.log('❌ yt-dlp method failed:', error.message);
    }
    
    console.log('\n❌ All methods failed for:', url);
    console.log('');
  }
}

// Direct Instagram extraction (local download only)
async function extractInstagramDirect(url) {
  // Convert to proper Instagram URL format
  let cleanUrl = url;
  if (!cleanUrl.endsWith('/')) {
    cleanUrl += '/';
  }
  
  const response = await axios.get(cleanUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    }
  });
  
  const html = response.data;
  
  // Enhanced patterns for Instagram video URL extraction
  const patterns = [
    /"video_url":"([^"]+)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /src:"([^"]*\.mp4[^"]*)"/g,
    /"video_versions":\[{"type":"101","url":"([^"]+)"/g,
    /"video_versions":\[{"type":"102","url":"([^"]+)"/g,
    /"video_dash_manifest":"([^"]+)"/g,
    /videoUrl":"([^"]+)"/g,
    /video_url":"([^"]+)"/g,
    /"playback_url":"([^"]+)"/g
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let videoUrl = match[1];
      if (videoUrl && videoUrl.includes('http')) {
        // Clean up the URL
        videoUrl = videoUrl.replace(/\\u0025/g, '%')
                          .replace(/\\u0026/g, '&')
                          .replace(/\\\//g, '/')
                          .replace(/\\/g, '');
        
        try {
          videoUrl = decodeURIComponent(videoUrl);
          console.log("Found potential video URL:", videoUrl.substring(0, 100) + "...");
          
          // Validate the URL
          if (await validateVideoUrl(videoUrl)) {
            const fileName = `instagram_video_${Date.now()}.mp4`;
            const success = await downloadVideoLocal(videoUrl, fileName);
            if (success) {
              return fileName;
            }
          }
        } catch (decodeError) {
          console.log("URL decode failed:", decodeError.message);
        }
      }
    }
  }
  
  // Try to extract from JSON data
  const jsonMatch = html.match(/window\._sharedData\s*=\s*({.+?});/);
  if (jsonMatch) {
    try {
      const sharedData = JSON.parse(jsonMatch[1]);
      const videoUrl = extractVideoFromSharedData(sharedData);
      if (videoUrl) {
        console.log("Found video URL from shared data:", videoUrl.substring(0, 100) + "...");
        const fileName = `instagram_video_${Date.now()}.mp4`;
        const success = await downloadVideoLocal(videoUrl, fileName);
        if (success) {
          return fileName;
        }
      }
    } catch (jsonError) {
      console.log("JSON parsing failed:", jsonError.message);
    }
  }
  
  return null;
}

// Mobile Instagram extraction
async function extractInstagramMobile(url) {
  // Convert to mobile URL
  const mobileUrl = url.replace('www.instagram.com', 'm.instagram.com');
  
  const response = await axios.get(mobileUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    }
  });
  
  const html = response.data;
  
  // Mobile-specific patterns
  const mobilePatterns = [
    /src="([^"]*\.mp4[^"]*)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /data-video-url="([^"]+)"/g,
    /video[^>]*src="([^"]+)"/g
  ];
  
  for (const pattern of mobilePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const videoUrl = match[1];
      if (videoUrl && await validateVideoUrl(videoUrl)) {
        console.log("Found mobile video URL:", videoUrl.substring(0, 100) + "...");
        const fileName = `instagram_video_mobile_${Date.now()}.mp4`;
        const success = await downloadVideoLocal(videoUrl, fileName);
        if (success) {
          return fileName;
        }
      }
    }
  }
  
  return null;
}

// Extract video URL from Instagram shared data
function extractVideoFromSharedData(sharedData) {
  try {
    const entryData = sharedData.entry_data;
    if (entryData && entryData.PostPage) {
      const postData = entryData.PostPage[0].graphql.shortcode_media;
      if (postData.is_video && postData.video_url) {
        return postData.video_url;
      }
    }
    
    // Try alternative structure
    if (entryData && entryData.ProfilePage) {
      // Handle profile page structure
      const profileData = entryData.ProfilePage[0].graphql.user;
      // This would need more specific handling based on Instagram's structure
    }
  } catch (error) {
    console.log("Error extracting from shared data:", error.message);
  }
  
  return null;
}

// yt-dlp extraction (local download only)
async function extractWithYtDlpLocal(url, platform) {
  try {
    // Check if yt-dlp is available
    let ytDlpPath = 'yt-dlp';
    try {
      await execAsync('yt-dlp --version');
    } catch (error) {
      // Try common user installation paths
      const commonPaths = [
        '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',
        '/usr/local/bin/yt-dlp',
        '~/.local/bin/yt-dlp'
      ];
      
      let found = false;
      for (const path of commonPaths) {
        try {
          await execAsync(`${path} --version`);
          ytDlpPath = path;
          found = true;
          break;
        } catch (pathError) {
          continue;
        }
      }
      
      if (!found) {
        throw new Error('yt-dlp is not installed or not in PATH');
      }
    }
    
    const fileName = `${platform.toLowerCase()}_video_ytdlp_${Date.now()}.mp4`;
    
    // Use yt-dlp to download the video
    const command = `${ytDlpPath} -f "best[ext=mp4]" -o "${fileName}" "${url}"`;
    
    console.log('Executing yt-dlp command...');
    const { stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      console.error('yt-dlp stderr:', stderr);
    }
    
    // Check if file was created
    if (fs.existsSync(fileName)) {
      const stats = fs.statSync(fileName);
      console.log(`File downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
      return fileName;
    } else {
      throw new Error('yt-dlp failed to download the video');
    }
    
  } catch (error) {
    throw new Error(`yt-dlp extraction failed: ${error.message}`);
  }
}

// Utility function to validate video URLs
async function validateVideoUrl(url) {
  try {
    const response = await axios.head(url, { timeout: 5000 });
    const contentType = response.headers['content-type'];
    return contentType && contentType.includes('video');
  } catch (error) {
    return false;
  }
}

// Download video locally
async function downloadVideoLocal(videoUrl, fileName) {
  try {
    console.log(`Downloading to: ${fileName}`);
    
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        'Referer': 'https://www.instagram.com/'
      },
      timeout: 30000
    });
    
    const writer = fs.createWriteStream(fileName);
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // Check if file was actually written
        const stats = fs.statSync(fileName);
        if (stats.size === 0) {
          reject(new Error("Downloaded file is empty"));
        } else {
          console.log(`✅ Downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
          resolve(true);
        }
      });
      writer.on('error', reject);
      
      // Add timeout for download
      setTimeout(() => {
        writer.destroy();
        reject(new Error("Download timeout"));
      }, 60000);
    });
  } catch (error) {
    throw new Error(`Error downloading video: ${error.message}`);
  }
}

if (require.main === module) {
  testInstagramDownloadOnly().catch(console.error);
}
