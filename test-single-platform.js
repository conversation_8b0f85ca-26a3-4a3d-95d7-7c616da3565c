const { extractVideoFromUrl } = require('./src/utils/videoUtils');

// Test a single platform with a specific URL
async function testSinglePlatform() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node test-single-platform.js <URL>');
    console.log('Example: node test-single-platform.js "https://www.facebook.com/watch?v=487026504219526"');
    return;
  }
  
  const url = args[0];
  console.log('Testing URL:', url);
  console.log('Platform:', detectPlatform(url));
  console.log('---');
  
  try {
    const result = await extractVideoFromUrl(url);
    console.log('✅ Success:', result);
  } catch (error) {
    console.log('❌ Error:', error.message);
    
    // Provide specific guidance based on the error
    if (error.message.includes('privacy settings')) {
      console.log('\n💡 Tip: Try with a public video that doesn\'t require login');
    } else if (error.message.includes('anti-scraping')) {
      console.log('\n💡 Tip: Consider using yt-dlp or a proxy service for production');
    } else if (error.message.includes('S3')) {
      console.log('\n💡 Tip: Configure AWS S3 credentials to complete the upload');
    }
  }
}

function detectPlatform(url) {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'YouTube';
  } else if (url.includes('instagram.com')) {
    return 'Instagram';
  } else if (url.includes('facebook.com')) {
    return 'Facebook';
  } else if (url.includes('twitter.com') || url.includes('x.com')) {
    return 'X/Twitter';
  } else {
    return 'Unknown';
  }
}

if (require.main === module) {
  testSinglePlatform().catch(console.error);
}
