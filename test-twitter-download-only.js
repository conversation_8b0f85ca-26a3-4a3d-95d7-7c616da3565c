const axios = require('axios');
const fs = require('fs');
const { v4 } = require('uuid');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Test X/Twitter video extraction without S3 upload
async function testTwitterDownloadOnly() {
  // Test URLs - replace with actual Twitter video URLs
  const testUrls = [
    'https://x.com/i/status/1937465796814316002', // Real Twitter video URL https://x.com/i/status/1937465796814316002
  ];
  
  console.log('🐦 Testing X/Twitter Video Download (Local Only)');
  console.log('');
  
  for (const url of testUrls) {
    if (url.includes('EXAMPLE')) {
      console.log('⚠️  Please replace EXAMPLE URLs with actual Twitter/X video URLs');
      continue;
    }
    
    console.log('URL:', url);
    console.log('');
    
    try {
      // Method 1: Try direct extraction
      console.log('📥 Method 1: Direct Twitter extraction...');
      const directResult = await extractTwitterDirect(url);
      if (directResult) {
        console.log('✅ SUCCESS with direct method!');
        console.log('Downloaded file:', directResult);
        continue;
      }
    } catch (error) {
      console.log('❌ Direct method failed:', error.message);
    }
    
    try {
      // Method 2: Try mobile Twitter
      console.log('\n📥 Method 2: Mobile Twitter extraction...');
      const mobileResult = await extractTwitterMobile(url);
      if (mobileResult) {
        console.log('✅ SUCCESS with mobile method!');
        console.log('Downloaded file:', mobileResult);
        continue;
      }
    } catch (error) {
      console.log('❌ Mobile method failed:', error.message);
    }
    
    try {
      // Method 3: Try API approach
      console.log('\n📥 Method 3: Twitter API extraction...');
      const apiResult = await extractTwitterAPI(url);
      if (apiResult) {
        console.log('✅ SUCCESS with API method!');
        console.log('Downloaded file:', apiResult);
        continue;
      }
    } catch (error) {
      console.log('❌ API method failed:', error.message);
    }
    
    try {
      // Method 4: Try yt-dlp
      console.log('\n📥 Method 4: yt-dlp extraction...');
      const ytDlpResult = await extractWithYtDlpLocal(url, 'Twitter');
      if (ytDlpResult) {
        console.log('✅ SUCCESS with yt-dlp!');
        console.log('Downloaded file:', ytDlpResult);
        continue;
      }
    } catch (error) {
      console.log('❌ yt-dlp method failed:', error.message);
    }
    
    console.log('\n❌ All methods failed for:', url);
    console.log('');
  }
}

// Direct Twitter extraction (local download only)
async function extractTwitterDirect(url) {
  // Normalize URL (convert x.com to twitter.com)
  let cleanUrl = url.replace('x.com', 'twitter.com');
  
  const response = await axios.get(cleanUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1'
    }
  });
  
  const html = response.data;
  
  // Enhanced patterns for Twitter video URL extraction
  const patterns = [
    /"video_url":"([^"]+)"/g,
    /"playback_url":"([^"]+)"/g,
    /"url":"([^"]*\.mp4[^"]*)"/g,
    /src:"([^"]*\.mp4[^"]*)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /"contentUrl":"([^"]+\.mp4[^"]*)"/g,
    /video[^>]*src="([^"]+)"/g,
    /"variants":\[{"bitrate":\d+,"content_type":"video\/mp4","url":"([^"]+)"/g,
    /"video_info":{"variants":\[{"bitrate":\d+,"content_type":"video\/mp4","url":"([^"]+)"/g
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let videoUrl = match[1];
      if (videoUrl && videoUrl.includes('http')) {
        // Clean up the URL
        videoUrl = videoUrl.replace(/\\u0025/g, '%')
                          .replace(/\\u0026/g, '&')
                          .replace(/\\\//g, '/')
                          .replace(/\\/g, '');
        
        try {
          videoUrl = decodeURIComponent(videoUrl);
          console.log("Found potential video URL:", videoUrl.substring(0, 100) + "...");
          
          // Validate the URL
          if (await validateVideoUrl(videoUrl)) {
            const fileName = `twitter_video_${Date.now()}.mp4`;
            const success = await downloadVideoLocal(videoUrl, fileName);
            if (success) {
              return fileName;
            }
          }
        } catch (decodeError) {
          console.log("URL decode failed:", decodeError.message);
        }
      }
    }
  }
  
  // Try to extract from JSON-LD structured data
  const jsonLdMatch = html.match(/<script type="application\/ld\+json">(.+?)<\/script>/g);
  if (jsonLdMatch) {
    for (const jsonScript of jsonLdMatch) {
      try {
        const jsonContent = jsonScript.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
        const data = JSON.parse(jsonContent);
        const videoUrl = extractVideoFromJsonLd(data);
        if (videoUrl) {
          console.log("Found video URL from JSON-LD:", videoUrl.substring(0, 100) + "...");
          const fileName = `twitter_video_${Date.now()}.mp4`;
          const success = await downloadVideoLocal(videoUrl, fileName);
          if (success) {
            return fileName;
          }
        }
      } catch (jsonError) {
        console.log("JSON-LD parsing failed:", jsonError.message);
      }
    }
  }
  
  return null;
}

// Mobile Twitter extraction
async function extractTwitterMobile(url) {
  // Convert to mobile URL
  const mobileUrl = url.replace('twitter.com', 'mobile.twitter.com').replace('x.com', 'mobile.twitter.com');
  
  const response = await axios.get(mobileUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
    }
  });
  
  const html = response.data;
  
  // Mobile-specific patterns
  const mobilePatterns = [
    /src="([^"]*\.mp4[^"]*)"/g,
    /"src":"([^"]*\.mp4[^"]*)"/g,
    /data-video-url="([^"]+)"/g,
    /video[^>]*src="([^"]+)"/g,
    /"video_url":"([^"]+)"/g
  ];
  
  for (const pattern of mobilePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const videoUrl = match[1];
      if (videoUrl && await validateVideoUrl(videoUrl)) {
        console.log("Found mobile video URL:", videoUrl.substring(0, 100) + "...");
        const fileName = `twitter_video_mobile_${Date.now()}.mp4`;
        const success = await downloadVideoLocal(videoUrl, fileName);
        if (success) {
          return fileName;
        }
      }
    }
  }
  
  return null;
}

// Twitter API extraction (using guest token approach)
async function extractTwitterAPI(url) {
  try {
    // Extract tweet ID from URL
    const tweetIdMatch = url.match(/status\/(\d+)/);
    if (!tweetIdMatch) {
      throw new Error('Could not extract tweet ID from URL');
    }
    
    const tweetId = tweetIdMatch[1];
    
    // Get guest token
    const guestTokenResponse = await axios.post('https://api.twitter.com/1.1/guest/activate.json', {}, {
      headers: {
        'Authorization': 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA',
        'Content-Type': 'application/json'
      }
    });
    
    const guestToken = guestTokenResponse.data.guest_token;
    
    // Get tweet data
    const tweetResponse = await axios.get(`https://api.twitter.com/1.1/statuses/show.json?id=${tweetId}&include_entities=true&tweet_mode=extended`, {
      headers: {
        'Authorization': 'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA',
        'x-guest-token': guestToken
      }
    });
    
    const tweetData = tweetResponse.data;
    
    // Extract video URL from tweet data
    if (tweetData.extended_entities && tweetData.extended_entities.media) {
      for (const media of tweetData.extended_entities.media) {
        if (media.type === 'video' && media.video_info && media.video_info.variants) {
          // Find the highest quality MP4 variant
          const mp4Variants = media.video_info.variants.filter(v => v.content_type === 'video/mp4');
          if (mp4Variants.length > 0) {
            // Sort by bitrate (highest first)
            mp4Variants.sort((a, b) => (b.bitrate || 0) - (a.bitrate || 0));
            const videoUrl = mp4Variants[0].url;
            
            console.log("Found video URL from API:", videoUrl.substring(0, 100) + "...");
            const fileName = `twitter_video_api_${Date.now()}.mp4`;
            const success = await downloadVideoLocal(videoUrl, fileName);
            if (success) {
              return fileName;
            }
          }
        }
      }
    }
    
    throw new Error('No video found in tweet data');
    
  } catch (error) {
    throw new Error(`Twitter API extraction failed: ${error.message}`);
  }
}

// Extract video URL from JSON-LD structured data
function extractVideoFromJsonLd(data) {
  try {
    if (data.contentUrl && data.contentUrl.includes('.mp4')) {
      return data.contentUrl;
    }
    
    if (data.video && data.video.contentUrl) {
      return data.video.contentUrl;
    }
    
    // Handle array of structured data
    if (Array.isArray(data)) {
      for (const item of data) {
        const videoUrl = extractVideoFromJsonLd(item);
        if (videoUrl) return videoUrl;
      }
    }
  } catch (error) {
    console.log("Error extracting from JSON-LD:", error.message);
  }
  
  return null;
}

// yt-dlp extraction (local download only)
async function extractWithYtDlpLocal(url, platform) {
  try {
    // Check if yt-dlp is available
    let ytDlpPath = 'yt-dlp';
    try {
      await execAsync('yt-dlp --version');
    } catch (error) {
      // Try common user installation paths
      const commonPaths = [
        '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',
        '/usr/local/bin/yt-dlp',
        '~/.local/bin/yt-dlp'
      ];
      
      let found = false;
      for (const path of commonPaths) {
        try {
          await execAsync(`${path} --version`);
          ytDlpPath = path;
          found = true;
          break;
        } catch (pathError) {
          continue;
        }
      }
      
      if (!found) {
        throw new Error('yt-dlp is not installed or not in PATH');
      }
    }
    
    const fileName = `${platform.toLowerCase()}_video_ytdlp_${Date.now()}.mp4`;
    
    // Use yt-dlp to download the video
    const command = `${ytDlpPath} -f "best[ext=mp4]" -o "${fileName}" "${url}"`;
    
    console.log('Executing yt-dlp command...');
    const { stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARNING')) {
      console.error('yt-dlp stderr:', stderr);
    }
    
    // Check if file was created
    if (fs.existsSync(fileName)) {
      const stats = fs.statSync(fileName);
      console.log(`File downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
      return fileName;
    } else {
      throw new Error('yt-dlp failed to download the video');
    }
    
  } catch (error) {
    throw new Error(`yt-dlp extraction failed: ${error.message}`);
  }
}

// Utility function to validate video URLs
async function validateVideoUrl(url) {
  try {
    const response = await axios.head(url, { timeout: 5000 });
    const contentType = response.headers['content-type'];
    return contentType && contentType.includes('video');
  } catch (error) {
    return false;
  }
}

// Download video locally
async function downloadVideoLocal(videoUrl, fileName) {
  try {
    console.log(`Downloading to: ${fileName}`);
    
    const response = await axios({
      method: 'GET',
      url: videoUrl,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer': 'https://twitter.com/'
      },
      timeout: 30000
    });
    
    const writer = fs.createWriteStream(fileName);
    response.data.pipe(writer);
    
    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // Check if file was actually written
        const stats = fs.statSync(fileName);
        if (stats.size === 0) {
          reject(new Error("Downloaded file is empty"));
        } else {
          console.log(`✅ Downloaded: ${fileName} (${Math.round(stats.size / 1024 / 1024 * 100) / 100} MB)`);
          resolve(true);
        }
      });
      writer.on('error', reject);
      
      // Add timeout for download
      setTimeout(() => {
        writer.destroy();
        reject(new Error("Download timeout"));
      }, 60000);
    });
  } catch (error) {
    throw new Error(`Error downloading video: ${error.message}`);
  }
}

if (require.main === module) {
  testTwitterDownloadOnly().catch(console.error);
}
