const { extractVideoFromUrl } = require('./src/utils/videoUtils');

// Test function to verify video extraction
async function testVideoExtraction() {
  console.log('Testing video extraction functionality...\n');

  // Test URLs (replace with actual URLs you want to test)
  const testUrls = [
    // {
    //   platform: 'YouTube',
    //   url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Example YouTube URL
    //   shouldWork: true
    // },
    // {
    //   platform: 'Instagram',
    //   url: 'https://www.instagram.com/p/EXAMPLE/', // Replace with actual Instagram post
    //   shouldWork: false // Currently requires additional setup
    // },
    {
      platform: 'Facebook',
      url: 'https://www.facebook.com/watch?v=487026504219526', // Replace with actual Facebook video
      shouldWork: false // Currently requires additional setup
    },
    // {
    //   platform: 'X/Twitter',
    //   url: 'https://x.com/user/status/EXAMPLE', // Replace with actual X/Twitter post
    //   shouldWork: false // Currently requires additional setup
    // }
  ];

  for (const test of testUrls) {
    console.log(`\n--- Testing ${test.platform} ---`);
    console.log(`URL: ${test.url}`);
    console.log(`Expected to work: ${test.shouldWork ? 'Yes' : 'No (requires setup)'}`);
    
    try {
      const result = await extractVideoFromUrl(test.url);
      console.log('✅ Success:', result);
    } catch (error) {
      console.log('❌ Error:', error.message);
      
      if (!test.shouldWork) {
        console.log('ℹ️  This is expected - additional setup required for this platform');
      }
    }
  }

  console.log('\n--- Test Summary ---');
  console.log('YouTube: Should work with valid URLs');
  console.log('Instagram: Requires additional setup (see error messages for details)');
  console.log('Facebook: Requires additional setup (see error messages for details)');
  console.log('X/Twitter: Requires additional setup (see error messages for details)');
}

// Run the test
if (require.main === module) {
  testVideoExtraction().catch(console.error);
}

module.exports = { testVideoExtraction };
